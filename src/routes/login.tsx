import { createFileRoute } from "@tanstack/react-router";
import LoginForm from "~/auth/components/LoginForm";

export const Route = createFileRoute("/login")({
	component: RouteComponent,
	head: () => ({
		meta: [
			{
				title: "Iniciar <PERSON>",
			},
		],
	}),
});

function RouteComponent() {
	return (
		<div className="hero min-h-screen bg-base-200">
			<div className="hero-content flex-col lg:flex-row-reverse">
				<div className="text-center lg:text-left">
					<h1 className="font-bold text-5xl">Ingresa ahora!</h1>
					<p className="py-6">
						Sistema de administración de horarios y sesiones psicológicas
					</p>
				</div>
				<div className="card w-full max-w-sm shrink-0 bg-base-100 shadow-2xl">
					<div className="card-body">
						<LoginForm />
					</div>
				</div>
			</div>
		</div>
	);
}
